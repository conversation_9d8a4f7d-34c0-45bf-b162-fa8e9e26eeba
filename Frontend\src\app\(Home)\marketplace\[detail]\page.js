import { Col, Container, Row } from "react-bootstrap";
import React from "react";
import CommonSearch from "@/Components/UI/CommonSearch";
import { CartIcon } from "@/assets/svgIcons/SvgIcon";
import CustomSelect from "@/Components/UI/Select";
import CustomBreadcrumb from "@/Components/UI/CustomBreadcrumb";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Marketplace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import MetaHead from "@/Seo/Meta/MetaHead";
import SellerInfoCard from "../commons/SellerInfoCard";
import Image from "next/image";
import { generateProductSchema, generateSchemaMetadata } from "@/Seo/Schema/JsonLdSchema";

const MarketplaceDetail = () => {
  const options = [
    {
      value: "Shop",
      label: "Shop",
    },
    {
      value: "Buy",
      label: "Buy",
    },
    {
      value: "Sell",
      label: "Sell",
    },
  ];

  const metaArray = {
    title: `Mastering the stock Marketplace | TradeReply`,
    description: `Discover Mastering the stock Marketplace in the TradeReply Marketplace. Enhance your trading with top-quality tools and services.`,
    canonical_link: `https://www.tradereply.com/marketplace/details`,
    og_site_name: "TradeReply",
    og_title: `Mastering the stock Marketplace | TradeReply Marketplace`,
    og_description: `Discover Mastering the stock market in the TradeReply Marketplace. Enhance your trading with top-quality tools and services.`,
    og_url: `https://www.tradereply.com/marketplace/details`,
    og_type: "product",
    twitter_title: `Mastering the stock Marketplace | TradeReply Marketplace`,
    twitter_description: `Discover Mastering the stock Marketplace in the TradeReply Marketplace. Enhance your trading with top-quality tools and services.`,
  };

  // Schema generation is now handled in generateMetadata function

  return (
    <>
      <MetaHead props={metaArray} />
      <HomeLayout>
        <div className="marketplace py-100">
          <Container>
            <section className="marketplace_inner">
              <div className="marketplace_heading text-center">
                <h1>TradeReply Marketplace</h1>
              </div>
              <div className="marketplace_shopcart d-flex align-items-center justify-content-between justify-content-md-center">
                <div className="marketplace_shopcart_selectshop">
                  <CustomSelect options={options} placeholder="Shop" />
                </div>
                <div className="marketplace_shopcart_btn order-md-last">
                  <button type="button" className="d-flex align-items-center">
                    <CartIcon />
                    Cart (0)
                  </button>
                </div>
                <div className="education_search">
                  <CommonSearch
                    placeholder="Explore Products & Strategies"
                    icon={true}
                    name={"marketDetail"}
                  />
                </div>
              </div>
              <div className="marketplace_inner_heading">
                <h4>TradeReply Courses</h4>
                <CustomBreadcrumb
                  href="#"
                  linkname="Shop"
                  pagename="Courses"
                  articlename={"Mastering the stock market"}
                />
              </div>
            </section>

            <div className="marketplace_products">
              <Row>
                <Col md={4} xs={12}>
                  <img
                    className="w-100"
                    src={
                      "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png"
                    }
                    alt={"TradeReply marketplace product"}
                  />
                  <div className="d-none d-md-block">
                    <SellerInfoCard
                      sellerName="Aaron McCloud"
                      sellerImage="/images/user-demo-img.jpg"
                      ratingCount={5}
                      totalReviews={201}
                    />
                  </div>
                </Col>
                <Col md={8} xs={12} className="mt-3 mt-md-0">
                  <h4>Mastering the stock market</h4>
                  <h1 className="mt-3">$11.95</h1>
                  <div className="marketplace_products_card_rating mt-3 pb-3 bb-blue">
                    <Image
                      src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg"
                      alt="star"
                      width={25}
                      height={25}
                    />
                    <span>200</span>
                  </div>
                  <h4 className="mt-md-5 mt-3">Quantity</h4>
                  <div className="d-flex gap-3 mt-3 bb-blue pb-mb-5 pb-3">
                    <select className="cart_select form-select">
                      <option>1</option>
                      <option>2</option>
                    </select>
                    <CommonButton
                      type="button"
                      title="Add To Cart"
                      className="cart_button"
                    />
                  </div>
                  <div className="pb-mb-5 pb-3 bb-blue">
                    <h4 className="my-4">Details</h4>
                    <h5 className="mt-3">This is a course meant to help</h5>
                    <h5 className="mt-3">This is a course meant to help</h5>
                    <h5 className="mt-3">This is a course meant to help</h5>
                    <h5 className="mt-3">This is a course meant to help</h5>
                    <h5 className="mt-3">This is a course meant to help</h5>
                  </div>
                  <div className="d-block d-md-none">
                    <SellerInfoCard
                      sellerName="Aaron McCloud"
                      sellerImage="/images/user-demo-img.jpg"
                      ratingCount={5}
                      totalReviews={201}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </Container>
        </div>
      </HomeLayout>
    </>
  );
};

export default MarketplaceDetail;

// Generate metadata with JSON-LD schema for marketplace product pages
export async function generateMetadata({ params }) {
  // In a real implementation, you would fetch product data based on params
  // For now, using the same mock data structure
  const productData = {
    name: "Mastering the stock market",
    description:
      "This is a course meant to help traders understand market fundamentals and advanced strategies.",
    image:
      "https://cdn.tradereply.com/dev/user-uploads/marketplace/listing-12345.png",
    brand: "Aaron McCloud",
    price: 11.95,
    currency: "USD",
    availability: "http://schema.org/InStock",
    url: "https://www.tradereply.com/marketplace/details",
    seller: {
      name: "Aaron McCloud",
      url: "https://www.tradereply.com/seller/aaron-mccloud",
    },
    aggregateRating: {
      ratingValue: 5.0,
      reviewCount: 201,
    },
  };

  // Generate Product schema with fallback support
  const productSchema = generateProductSchema({
    name: productData.name,
    description: productData.description,
    image: productData.image,
    brand: productData.brand,
    price: productData.price,
    currency: productData.currency,
    availability: productData.availability,
    url: productData.url,
    seller: productData.seller,
    aggregateRating: productData.aggregateRating,
    productData: productData, // Pass full product data for fallback generation
  });

  // Generate schema metadata
  let schemaMetadata = {};
  if (productSchema) {
    schemaMetadata = generateSchemaMetadata([productSchema]);
  }

  return {
    title: `${productData.name} | TradeReply Marketplace`,
    description: productData.description,
    robots: "index, follow",
    openGraph: {
      title: `${productData.name} | TradeReply Marketplace`,
      description: productData.description,
      siteName: 'TradeReply',
      type: 'product',
      images: [
        {
          url: productData.image,
          width: 1200,
          height: 630,
          alt: productData.name,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${productData.name} | TradeReply Marketplace`,
      description: productData.description,
      images: [productData.image],
    },
    // Merge schema metadata into the main metadata object
    ...schemaMetadata
  };
}
