# Dynamic JSON-LD Schemas in HTML `<head>` - Next.js 15 App Router

This document explains how to implement dynamic JSON-LD schemas in the HTML `<head>` section using Next.js 15 App Router's Metadata API.

## Overview

The solution uses Next.js 15's **Metadata API** with the `other` field to inject JSON-LD schemas directly into the `<head>` section. This approach ensures:

- ✅ Schemas are placed in the HTML `<head>` section (not `<body>`)
- ✅ Each page can have dynamic, page-specific schemas
- ✅ Schemas are sourced from the existing `JsonLdSchema.js` file
- ✅ Works with Next.js 15 App Router
- ✅ Follows TradeReply's pattern of separate script tags per schema
- ✅ Maintains SSR compatibility
- ✅ SEO best practices compliance

## Implementation Pattern

### 1. Schema Metadata Utility Function

The `generateSchemaMetadata()` function in `JsonLdSchema.js` converts schema objects into Next.js metadata format:

```javascript
import { generateSchemaMetadata } from "@/Seo/Schema/JsonLdSchema";

// Generate metadata object with JSON-LD schemas
const schemaMetadata = generateSchemaMetadata([blogSchema, organizationSchema]);
```

### 2. Page Implementation Pattern

Each page follows this pattern:

```javascript
// Import required schema generators and metadata utility
import { 
  generateBlogPostingSchema, 
  generateSchemaMetadata 
} from "@/Seo/Schema/JsonLdSchema";

// Page component (clean, no schema generation)
export default async function BlogDetail({ params }) {
  const data = await fetchData(params);
  
  return (
    <PageContent data={data} />
  );
}

// Generate metadata with schemas
export async function generateMetadata({ params }) {
  const data = await fetchData(params);
  
  // Generate schemas
  const blogSchema = generateBlogPostingSchema({
    canonicalUrl: `https://www.tradereply.com/blog/${data.slug}`,
    headline: data.title,
    description: data.summary,
    // ... other schema properties
  });
  
  // Convert schemas to metadata format
  const schemaMetadata = generateSchemaMetadata([blogSchema]);
  
  return {
    title: `${data.title} | TradeReply Blog`,
    description: data.summary,
    // ... other metadata
    
    // Merge schema metadata
    ...schemaMetadata
  };
}
```

## Updated Pages

The following pages have been updated to use this new pattern:

### 1. Blog Detail Page (`/blog/[detail]/page.js`)
- ✅ Generates `BlogPosting` schema dynamically
- ✅ Uses fallback generation for missing data
- ✅ Schemas injected into `<head>` via metadata API

### 2. Marketplace Detail Page (`/marketplace/[detail]/page.js`)
- ✅ Generates `Product` schema with reviews and ratings
- ✅ Includes seller information and pricing
- ✅ Schemas injected into `<head>` via metadata API

### 3. Category Page (`/category/page.js`)
- ✅ Generates `CollectionPage` and `BreadcrumbList` schemas
- ✅ Handles pagination and search states
- ✅ Schemas injected into `<head>` via metadata API

### 4. Education Detail Page (`/education/[detail]/page.js`)
- ✅ Generates `Article` schema for educational content
- ✅ Includes publication dates and author information
- ✅ Schemas injected into `<head>` via metadata API

## Key Benefits

1. **SEO Compliance**: Schemas are properly placed in `<head>` section
2. **Clean Components**: Page components focus on rendering, not schema generation
3. **SSR Compatible**: Schemas are generated server-side during metadata generation
4. **Dynamic Content**: Each page can have unique schemas based on data
5. **Maintainable**: Uses existing schema generators from `JsonLdSchema.js`
6. **Performance**: Schemas are generated once during SSR, not on every render

## Migration Guide

To migrate existing pages to this pattern:

1. **Remove schema generation from page components**:
   ```javascript
   // Remove this from component
   const schema = generateSchema(data);
   return (
     <>
       {schema && <JsonLdSchema schemas={[schema]} />}
       <PageContent />
     </>
   );
   ```

2. **Add generateMetadata function**:
   ```javascript
   export async function generateMetadata({ params }) {
     const data = await fetchData(params);
     const schema = generateSchema(data);
     const schemaMetadata = generateSchemaMetadata([schema]);
     
     return {
       title: data.title,
       description: data.description,
       ...schemaMetadata
     };
   }
   ```

3. **Clean up component**:
   ```javascript
   export default async function Page({ params }) {
     const data = await fetchData(params);
     return <PageContent data={data} />;
   }
   ```

## Testing

To verify schemas are working correctly:

1. **View Page Source**: Check that `<script type="application/ld+json">` tags appear in `<head>`
2. **Google Rich Results Test**: Use Google's testing tool to validate schemas
3. **Schema.org Validator**: Verify schema structure and completeness

## Next Steps

1. Update remaining pages to use this pattern
2. Add schema generation for other content types (products, events, etc.)
3. Implement schema testing in CI/CD pipeline
4. Monitor search console for rich results performance

## Notes

- The `generateSchemaMetadata()` function handles the conversion from schema objects to Next.js metadata format
- Each schema gets its own script tag in the head (following TradeReply's established pattern)
- Fallback data generation is preserved from the existing schema generators
- The pattern is compatible with both static and dynamic routes
