# Dynamic JSON-LD Schemas in HTML `<head>` - Next.js 15 App Router

This document explains how to implement dynamic JSON-LD schemas in the HTML `<head>` section using Next.js 15 App Router with the Script component.

## Overview

The solution uses Next.js **Script component** with `strategy="beforeInteractive"` to inject JSON-LD schemas directly into the `<head>` section. This approach ensures:

- ✅ Schemas are placed in the HTML `<head>` section (not `<body>`)
- ✅ Each page can have dynamic, page-specific schemas
- ✅ Schemas are sourced from the existing `JsonLdSchema.js` file
- ✅ Works with Next.js 15 App Router
- ✅ Follows TradeReply's pattern of separate script tags per schema
- ✅ Maintains SSR compatibility
- ✅ SEO best practices compliance

## Implementation Pattern

### 1. Import Required Dependencies

```javascript
import { generateBlogPostingSchema, formatDateToISO, getBlogSlug } from "@/Seo/Schema/JsonLdSchema";
import Script from "next/script";
```

### 2. Page Implementation Pattern

Each page follows this pattern:

```javascript
export default async function BlogDetail({ params }) {
  const data = await fetchData(params);

  // Generate schemas
  const blogSchema = generateBlogPostingSchema({
    canonicalUrl: `https://www.tradereply.com/blog/${data.slug}`,
    headline: data.title,
    description: data.summary,
    // ... other schema properties
  });

  return (
    <>
      {blogSchema && (
        <Script
          id="blog-schema"
          type="application/ld+json"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(blogSchema, null, 0)
          }}
        />
      )}
      <PageContent data={data} />
    </>
  );
}
```

### 3. Multiple Schemas Pattern

For pages with multiple schemas (like category pages):

```javascript
return (
  <>
    {categorySchemas.map((schema, index) => (
      <Script
        key={index}
        id={`category-schema-${index}`}
        type="application/ld+json"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schema, null, 0)
        }}
      />
    ))}
    <PageContent />
  </>
);
```

## Updated Pages

The following pages have been updated to use this new pattern:

### 1. Blog Detail Page (`/blog/[detail]/page.js`)
- ✅ Generates `BlogPosting` schema dynamically
- ✅ Uses fallback generation for missing data
- ✅ Schemas injected into `<head>` via Script component

### 2. Marketplace Detail Page (`/marketplace/[detail]/page.js`)
- ✅ Generates `Product` schema with reviews and ratings
- ✅ Includes seller information and pricing
- ✅ Schemas injected into `<head>` via Script component

### 3. Category Page (`/category/page.js`)
- ✅ Generates `CollectionPage` and `BreadcrumbList` schemas
- ✅ Handles pagination and search states
- ✅ Schemas injected into `<head>` via Script component

### 4. Education Detail Page (`/education/[detail]/page.js`)
- ✅ Uses existing Script implementation (already working)
- ✅ Generates `Article` schema for educational content
- ✅ Includes publication dates and author information

## Key Benefits

1. **SEO Compliance**: Schemas are properly placed in `<head>` section
2. **Simple Implementation**: Uses standard Next.js Script component
3. **SSR Compatible**: Scripts are rendered server-side with `beforeInteractive` strategy
4. **Dynamic Content**: Each page can have unique schemas based on data
5. **Maintainable**: Uses existing schema generators from `JsonLdSchema.js`
6. **Performance**: Schemas load before page becomes interactive

## Migration Guide

To migrate existing pages to this pattern:

1. **Import Script component**:
   ```javascript
   import Script from "next/script";
   ```

2. **Replace JsonLdSchema component with Script**:
   ```javascript
   // Replace this:
   {schema && <JsonLdSchema schemas={[schema]} />}

   // With this:
   {schema && (
     <Script
       id="page-schema"
       type="application/ld+json"
       strategy="beforeInteractive"
       dangerouslySetInnerHTML={{
         __html: JSON.stringify(schema, null, 0)
       }}
     />
   )}
   ```

3. **For multiple schemas**:
   ```javascript
   {schemas.map((schema, index) => (
     <Script
       key={index}
       id={`schema-${index}`}
       type="application/ld+json"
       strategy="beforeInteractive"
       dangerouslySetInnerHTML={{
         __html: JSON.stringify(schema, null, 0)
       }}
     />
   ))}
   ```

## Testing

To verify schemas are working correctly:

1. **View Page Source**: Check that `<script type="application/ld+json">` tags appear in `<head>`
2. **Google Rich Results Test**: Use Google's testing tool to validate schemas
3. **Schema.org Validator**: Verify schema structure and completeness

## Next Steps

1. Update remaining pages to use this pattern
2. Add schema generation for other content types (products, events, etc.)
3. Implement schema testing in CI/CD pipeline
4. Monitor search console for rich results performance

## Notes

- The `strategy="beforeInteractive"` ensures scripts are loaded in the `<head>` section before the page becomes interactive
- Each schema gets its own script tag (following TradeReply's established pattern)
- Fallback data generation is preserved from the existing schema generators
- The pattern is compatible with both static and dynamic routes
- Scripts are rendered server-side, ensuring they're available for search engine crawlers
