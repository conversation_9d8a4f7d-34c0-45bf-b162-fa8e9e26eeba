import { Suspense } from "react";
import InactivityHandler from "../utils/inactivityHandler";
import Script from "next/script";
import "bootstrap/dist/css/bootstrap.min.css";
import "./globals.scss";
import "../../src/css/app.scss";
import Providers from "@/Components/providers/Providers";
import MetaProvider from "@/Components/providers/MetaProvider";
import I18nProvider from "@/providers/I18nProvider";
import { Toaster } from "react-hot-toast";
import "@/lib/useTranslation";
import { LanguageProvider } from "@/context/LanguageContext";
import MetaHead from "@/Seo/Meta/MetaHead";
import ClientSideCanonicalTag from "@/Components/ClientSideCanonicalTag";
import AuthHeartbeatProvider from "@/Components/auth/AuthHeartbeatProvider";
import {
  generateOrganizationSchema,
  generateWebsiteSchema,
} from "@/Seo/Schema/JsonLdSchema";


const defaultSchemas = [generateOrganizationSchema(), generateWebsiteSchema()];
const defaultMetaProps = {
  title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
  description:
    "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
  canonical_link: "https://www.tradereply.com/",
  og_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
  og_description:
    "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
  og_site_name: "TradeReply",
  twitter_title: "TradeReply: Advanced Trading Tools & Strategy Optimization",
  twitter_description:
    "Optimize your trades with TradeReply.com. Access powerful trading strategies, real‑time analytics, and tools for crypto and stock market success.",
};

export default function RootLayout({ children, metaProps, schemas }) {
  return (
    <html lang="en">
      <head>
        <Script
          src="https://cmp.osano.com/fT1j7UREwV/19bf3e9d-4e98-4a0a-86cc-abeacfca9b84/osano.js"
          strategy="beforeInteractive"
        />
        <Suspense fallback={<></>}>
          <ClientSideCanonicalTag />
        </Suspense>
        <MetaHead
          props={metaProps ?? defaultMetaProps}
          schemas={schemas ?? defaultSchemas}
        />
      </head>
      <body
      // className={gilroy.variable}
      >
        <Providers>
          <MetaProvider>
            <I18nProvider>
              <LanguageProvider>
                <InactivityHandler />
                <Toaster
                  position="top-right"
                  reverseOrder={false}
                  toastOptions={{
                    style: {
                      zIndex: 99999,
                    },
                  }}
                />
                <AuthHeartbeatProvider>{children}</AuthHeartbeatProvider>
              </LanguageProvider>
            </I18nProvider>
          </MetaProvider>
        </Providers>
      </body>
    </html>
  );
}
